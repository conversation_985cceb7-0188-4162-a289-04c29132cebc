/**
 * 统一图标系统
 * 支持Vue组件使用和TS/JS中的字符串使用
 *
 * @example Vue组件使用
 * ```vue
 * <template>
 *   <Icon name="search" size="24" color="#1890ff" />
 * </template>
 * ```
 *
 * @example TS/JS使用
 * ```typescript
 * import { getIconSvg, createIconElement } from '@/icons';
 * const svg = getIconSvg('search');
 * const element = createIconElement('add', { size: '20px' });
 * ```
 */

// 图标SVG字符串映射
export const iconSvgs = {
  // Fluent Icons (保持原有命名风格)
  'TextHeader120Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M16.573 3.823a.75.75 0 0 0-1.058.53c-.255 1.138-1.308 2.608-2.681 3.523a.75.75 0 1 0 .832 1.248A8.769 8.769 0 0 0 15.5 7.47V15.5a.75.75 0 0 0 1.5 0V4.516a.75.75 0 0 0-.427-.693zM3.5 4.5a.75.75 0 1 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 1 0-1.5 0V9h-5V4.5z" fill="currentColor"></path></g></svg>',

  'TextHeader220Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm11.25.75c-1.292 0-2.25 1.124-2.25 2.25a.75.75 0 0 1-1.5 0c0-1.874 1.551-3.75 3.75-3.75c1.403 0 2.475.793 2.973 1.915c.49 1.106.41 2.488-.33 3.72c-.385.643-.958 1.16-1.527 1.607c-.265.209-.545.414-.816.613l-.067.049c-.295.217-.582.43-.858.65c-.892.715-1.569 1.449-1.794 2.446h4.919a.75.75 0 0 1 0 1.5H11.5a.75.75 0 0 1-.75-.75c0-2.099 1.226-3.396 2.437-4.366c.303-.243.614-.473.909-.69l.062-.045c.276-.202.535-.393.78-.586c.534-.42.929-.799 1.169-1.199c.51-.85.52-1.718.244-2.341c-.27-.608-.822-1.023-1.601-1.023z" fill="currentColor"></path></g></svg>',

  'TextHeader320Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M3.5 4.5a.75.75 0 0 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 0 0-1.5 0V9h-5V4.5zm8.97 1.958c.086-.295.216-.573.467-.784c.245-.206.693-.424 1.563-.424c.777 0 1.257.3 1.555.648c.32.374.445.825.445 1.102c0 .356-.091.92-.448 1.38c-.327.423-.965.87-2.302.87a.75.75 0 0 0 0 1.5c.446 0 1.198.11 1.81.42c.59.298.94.711.94 1.33c0 .84-.258 1.385-.593 1.72c-.338.338-.824.53-1.407.53c-.68 0-1.152-.116-1.458-.3c-.275-.164-.47-.414-.557-.847a.75.75 0 1 0-1.47.294c.163.817.593 1.442 1.255 1.84c.632.379 1.41.513 2.23.513c.917 0 1.806-.308 2.468-.97c.665-.665 1.032-1.62 1.032-2.78c0-1.234-.695-2.034-1.481-2.512c.283-.201.522-.434.72-.689C17.868 8.485 18 7.551 18 7c0-.63-.25-1.428-.805-2.077c-.577-.675-1.472-1.173-2.695-1.173c-1.13 0-1.95.29-2.528.776c-.571.48-.816 1.078-.942 1.516a.75.75 0 0 0 1.44.416z" fill="currentColor"></path></g></svg>',

  'TextBold20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5 4.5A1.5 1.5 0 0 1 6.5 3h3.88c2.364 0 4.12 1.934 4.12 4.12c0 .819-.247 1.606-.68 2.269c.842.749 1.427 1.849 1.427 3.241c0 2.775-2.318 4.37-4.367 4.37H6.5A1.5 1.5 0 0 1 5 15.5v-11zM8 6v2.25h2.38c.625 0 1.12-.516 1.12-1.13A1.12 1.12 0 0 0 10.38 6H8zm0 5.25V14h2.88c.691 0 1.367-.537 1.367-1.37c0-.84-.684-1.38-1.367-1.38H8z" fill="currentColor"></path></g></svg>',

  'TextItalic20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M8 3.25a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.235L8.592 15.5h2.658a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.235L11.408 4H8.75A.75.75 0 0 1 8 3.25z" fill="currentColor"></path></g></svg>',

  'TextUnderline24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6 4.5a1 1 0 0 1 2 0v6.001c-.003 3.463 1.32 4.999 4.247 4.999c2.928 0 4.253-1.537 4.253-5v-6a1 1 0 1 1 2 0v6c0 4.54-2.18 7-6.253 7S5.996 15.039 6 10.5v-6zM7 21a1 1 0 1 1 0-2h10.5a1 1 0 1 1 0 2H7z" fill="currentColor"></path></g></svg>',

  'TextStrikethrough20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M6.252 3.702A6.56 6.56 0 0 1 10 2.5c2.783 0 4.489 1.485 5.1 2.3a.75.75 0 0 1-1.2.9C13.511 5.182 12.217 4 10 4a5.06 5.06 0 0 0-2.877.923C6.331 5.489 6 6.105 6 6.5c0 .78.376 1.285 1.11 1.71c.18.105.377.2.586.29H5.162c-.408-.523-.662-1.178-.662-2c0-1.105.794-2.114 1.752-2.798zM16.5 10a.75.75 0 0 1 0 1.5h-1.662c.408.523.662 1.178.662 2c0 1.358-.874 2.376-1.912 3.014c-1.042.641-2.367.986-3.588.986c-1.142 0-2.133-.129-2.992-.498c-.877-.378-1.563-.982-2.132-1.836a.75.75 0 1 1 1.248-.832c.43.646.901 1.042 1.477 1.29c.594.255 1.354.376 2.4.376c.966 0 2.015-.28 2.801-.764C13.593 14.75 14 14.141 14 13.5c0-.78-.376-1.285-1.11-1.71c-.18-.105-.377-.2-.586-.29H3.5a.75.75 0 0 1 0-1.5h13z" fill="currentColor"></path></g></svg>',

  'Code20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M12.937 4.052a.75.75 0 0 0-1.373-.604l-5.5 12.5a.75.75 0 1 0 1.372.604l5.5-12.5zm1.356 9.793a.75.75 0 0 1-.137-1.052L16.303 10l-2.148-2.793a.75.75 0 0 1 1.188-.914l2.5 3.25a.75.75 0 0 1 0 .915l-2.5 3.25a.75.75 0 0 1-1.051.137zm-8.586-7.69a.75.75 0 0 1 .137 1.053L3.696 10l2.148 2.793a.75.75 0 1 1-1.188.915l-2.5-3.25a.75.75 0 0 1 0-.915l2.5-3.25a.75.75 0 0 1 1.051-.137z" fill="currentColor"></path></g></svg>',

  'Image28Regular': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 28 28"><g fill="none"><path d="M21.75 3A3.25 3.25 0 0 1 25 6.25v15.5A3.25 3.25 0 0 1 21.75 25H6.25A3.25 3.25 0 0 1 3 21.75V6.25A3.25 3.25 0 0 1 6.25 3h15.5zm.583 20.4l-7.807-7.68a.75.75 0 0 0-.968-.07l-.084.07l-7.808 7.68c.183.065.38.1.584.1h15.5c.204 0 .4-.035.583-.1l-7.807-7.68l7.807 7.68zM21.75 4.5H6.25A1.75 1.75 0 0 0 4.5 6.25v15.5c0 .208.036.408.103.593l7.82-7.692a2.25 2.25 0 0 1 3.026-.117l.129.117l7.82 7.692c.066-.185.102-.385.102-.593V6.25a1.75 1.75 0 0 0-1.75-1.75zm-3.25 3a2.5 2.5 0 1 1 0 5a2.5 2.5 0 0 1 0-5zm0 1.5a1 1 0 1 0 0 2a1 1 0 0 0 0-2z" fill="currentColor"></path></g></svg>',

  'ArrowUndo16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3 2.75a.75.75 0 0 1 1.5 0v3.095l2.673-2.673a4 4 0 0 1 5.657 5.656l-4.95 4.95a.75.75 0 1 1-1.06-1.06l4.95-4.95a2.5 2.5 0 0 0-3.536-3.536L5.966 6.5H8.25a.75.75 0 0 1 0 1.5h-4.4A.85.85 0 0 1 3 7.15v-4.4z" fill="currentColor"></path></g></svg>',

  'ArrowRedo16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M13.002 2.75a.75.75 0 0 0-1.5 0v3.095L8.828 3.172a4 4 0 0 0-5.656 5.656l4.95 4.95a.75.75 0 1 0 1.06-1.06l-4.95-4.95a2.5 2.5 0 0 1 3.536-3.536L10.036 6.5H7.75a.75.75 0 0 0 0 1.5h4.4c.47 0 .85-.38.85-.85v-4.4z" fill="currentColor"></path></g></svg>',

  'LineHorizontal120Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M2 9.75A.75.75 0 0 1 2.75 9h14.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 9.75z" fill="currentColor"></path></g></svg>',

  'VideoClip24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6.25A3.25 3.25 0 0 1 5.25 3h13.5A3.25 3.25 0 0 1 22 6.25v11.5A3.25 3.25 0 0 1 18.75 21H5.25A3.25 3.25 0 0 1 2 17.75V6.25zm7.5 3.134v5.231c0 .57.61.932 1.11.658l4.786-2.616a.75.75 0 0 0 0-1.316L10.61 8.726a.75.75 0 0 0-1.11.658z" fill="currentColor"></path></g></svg>',

  'FullScreenMaximize16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M4 3.5a.5.5 0 0 0-.5.5v1.614a.75.75 0 0 1-1.5 0V4a2 2 0 0 1 2-2h1.614a.75.75 0 0 1 0 1.5H4zm5.636-.75a.75.75 0 0 1 .75-.75H12a2 2 0 0 1 2 2v1.614a.75.75 0 0 1-1.5 0V4a.5.5 0 0 0-.5-.5h-1.614a.75.75 0 0 1-.75-.75zM2.75 9.636a.75.75 0 0 1 .75.75V12a.5.5 0 0 0 .5.5h1.614a.75.75 0 0 1 0 1.5H4a2 2 0 0 1-2-2v-1.614a.75.75 0 0 1 .75-.75zm10.5 0a.75.75 0 0 1 .75.75V12a2 2 0 0 1-2 2h-1.614a.75.75 0 1 1 0-1.5H12a.5.5 0 0 0 .5-.5v-1.614a.75.75 0 0 1 .75-.75z" fill="currentColor"></path></g></svg>',

  'ResizeSmall20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M5.5 4A1.5 1.5 0 0 0 4 5.5v1a.5.5 0 0 1-1 0v-1A2.5 2.5 0 0 1 5.5 3h1a.5.5 0 0 1 0 1h-1zm3 3A1.5 1.5 0 0 0 7 8.5v3A1.5 1.5 0 0 0 8.5 13h3a1.5 1.5 0 0 0 1.5-1.5v-3A1.5 1.5 0 0 0 11.5 7h-3zm6-3A1.5 1.5 0 0 1 16 5.5v1a.5.5 0 0 0 1 0v-1A2.5 2.5 0 0 0 14.5 3h-1a.5.5 0 0 0 0 1h1zm0 12a1.5 1.5 0 0 0 1.5-1.5v-1a.5.5 0 0 1 1 0v1a2.5 2.5 0 0 1-2.5 2.5h-1a.5.5 0 0 1 0-1h1zm-9 0A1.5 1.5 0 0 1 4 14.5v-1.25a.5.5 0 0 0-1 0v1.25A2.5 2.5 0 0 0 5.5 17h1.25a.5.5 0 0 0 0-1H5.5z" fill="currentColor"></path></g></svg>',

  'TextBulletListLtr16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M2.25 5a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm0 4.25a1.25 1.25 0 1 0 0-2.5a1.25 1.25 0 0 0 0 2.5zm1.25 3a1.25 1.25 0 1 1-2.5 0a1.25 1.25 0 0 1 2.5 0zM5.75 3a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5zM5 8a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 8zm.75 3.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5z" fill="currentColor"></path></g></svg>',

  'TextNumberListLtr16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M3.684 1.01c.193.045.33.21.33.402v3.294a.42.42 0 0 1-.428.412a.42.42 0 0 1-.428-.412V2.58a3.11 3.11 0 0 1-.664.435a.436.436 0 0 1-.574-.184a.405.405 0 0 1 .192-.552c.353-.17.629-.432.82-.661a2.884 2.884 0 0 0 .27-.388a.44.44 0 0 1 .482-.22zm-1.53 6.046a.401.401 0 0 1 0-.582l.002-.001V6.47l.004-.002l.008-.008a1.12 1.12 0 0 1 .103-.084a2.2 2.2 0 0 1 1.313-.435h.007c.32.004.668.084.947.283c.295.21.485.536.485.951c0 .452-.207.767-.488.992c-.214.173-.49.303-.714.409c-.036.016-.07.033-.103.049c-.267.128-.468.24-.61.39a.763.763 0 0 0-.147.22h1.635a.42.42 0 0 1 .427.411a.42.42 0 0 1-.428.412H2.457a.42.42 0 0 1-.428-.412c0-.51.17-.893.446-1.184c.259-.275.592-.445.86-.574c.043-.02.085-.04.124-.06c.231-.11.4-.19.529-.293c.12-.097.18-.193.18-.36c0-.148-.057-.23-.14-.289a.816.816 0 0 0-.448-.122a1.32 1.32 0 0 0-.818.289l-.005.005a.44.44 0 0 1-.602-.003zm.94 5.885a.42.42 0 0 1 .427-.412c.294 0 .456-.08.537-.15a.303.303 0 0 0 .11-.246c-.006-.16-.158-.427-.647-.427c-.352 0-.535.084-.618.137a.349.349 0 0 0-.076.062l-.003.004a.435.435 0 0 0 .01-.018v.001l-.002.002l-.002.004l-.003.006l-.005.008l.002-.003a.436.436 0 0 1-.563.165a.405.405 0 0 1-.191-.552v-.002l.002-.003l.003-.006l.008-.013a.71.71 0 0 1 .087-.12c.058-.067.142-.146.259-.22c.238-.153.59-.276 1.092-.276c.88 0 1.477.556 1.502 1.22c.012.303-.1.606-.339.84c.238.232.351.535.34.838c-.026.664-.622 1.22-1.503 1.22c-.502 0-.854-.122-1.092-.275a1.19 1.19 0 0 1-.326-.308a.71.71 0 0 1-.02-.033l-.008-.013l-.003-.005l-.001-.003v-.001l-.001-.001a.405.405 0 0 1 .19-.553a.436.436 0 0 1 .564.165l.003.004c.01.01.033.035.076.063c.083.053.266.137.618.137c.489 0 .641-.268.648-.428a.303.303 0 0 0-.11-.245c-.082-.072-.244-.151-.538-.151a.42.42 0 0 1-.427-.412zM7.75 3a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5zm0 4a.75.75 0 0 0 0 1.5h5.5a.75.75 0 0 0 0-1.5h-5.5z" fill="currentColor"></path></g></svg>',

  'TaskListLtr24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M6.707 3.293a1 1 0 0 0-1.414 0L4 4.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm14.296 13.7H10L9.883 17A1 1 0 0 0 10 18.993h11.003l.117-.006a1 1 0 0 0-.117-1.994zm0-5.993H10l-.117.007A1 1 0 0 0 10 13h11.003l.117-.007A1 1 0 0 0 21.003 11zm0-6H10l-.117.007A1 1 0 0 0 10 7h11.003l.117-.007A1 1 0 0 0 21.003 5zM6.707 16.293a1 1 0 0 0-1.414 0L4 17.586l-.293-.293a1 1 0 0 0-1.414 1.414l1 1a1 1 0 0 0 1.414 0l2-2a1 1 0 0 0 0-1.414zm-1.414-6.5a1 1 0 0 1 1.414 1.414l-2 2a1 1 0 0 1-1.414 0l-1-1a1 1 0 1 1 1.414-1.414l.293.293l1.293-1.293z" fill="currentColor"></path></g></svg>',

  'TextAlignLeft24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',

  'TextAlignCenter24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M4 6a1 1 0 0 1 1-1h14a1 1 0 1 1 0 2H5a1 1 0 0 1-1-1zm2 12a1 1 0 0 1 1-1h10a1 1 0 1 1 0 2H7a1 1 0 0 1-1-1zm-3-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',

  'TextAlignRight24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M5 6a1 1 0 0 1 1-1h15a1 1 0 1 1 0 2H6a1 1 0 0 1-1-1zm4 12a1 1 0 0 1 1-1h11a1 1 0 1 1 0 2H10a1 1 0 0 1-1-1zm-6-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',

  'TextAlignJustify24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M2 6a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm0 12a1 1 0 0 1 1-1h18a1 1 0 1 1 0 2H3a1 1 0 0 1-1-1zm1-7a1 1 0 1 0 0 2h18a1 1 0 1 0 0-2H3z" fill="currentColor"></path></g></svg>',

  'Search24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M10 2.5a7.5 7.5 0 0 1 5.964 12.048l4.743 4.745a1 1 0 0 1-1.32 1.497l-.094-.083l-4.745-4.743A7.5 7.5 0 1 1 10 2.5zm0 2a5.5 5.5 0 1 0 0 11a5.5 5.5 0 0 0 0-11z" fill="currentColor"></path></g></svg>',

  'Add24Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M11.883 3.007L12 3a1 1 0 0 1 .993.883L13 4v7h7a1 1 0 0 1 .993.883L21 12a1 1 0 0 1-.883.993L20 13h-7v7a1 1 0 0 1-.883.993L12 21a1 1 0 0 1-.993-.883L11 20v-7H4a1 1 0 0 1-.993-.883L3 12a1 1 0 0 1 .883-.993L4 11h7V4a1 1 0 0 1 .883-.993L12 3l-.117.007z" fill="currentColor"></path></g></svg>',

  'DocumentEdit16Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 16 16"><g fill="none"><path d="M8 1v3.5A1.5 1.5 0 0 0 9.498 6h3.5v1.035a2.548 2.548 0 0 0-1.37.712l-4.287 4.287a3.777 3.777 0 0 0-.994 1.755l-.302 1.209H4.5a1.5 1.5 0 0 1-1.5-1.5V2.5A1.5 1.5 0 0 1 4.5 1H8zm4.998 7.06c-.242.071-.47.203-.662.394L8.05 12.74a2.777 2.777 0 0 0-.73 1.29l-.303 1.211a.61.61 0 0 0 .738.74l1.211-.303a2.776 2.776 0 0 0 1.29-.73l4.288-4.288a1.56 1.56 0 0 0-1.545-2.6zm-4-6.81V4.5a.5.5 0 0 0 .5.5h3.25l-3.75-3.75z" fill="currentColor"></path></g></svg>',

  'Star48Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48"><g fill="none"><path d="M21.803 6.085c.899-1.82 3.495-1.82 4.394 0l4.852 9.832l10.85 1.577c2.01.292 2.813 2.762 1.358 4.179l-7.85 7.653l1.853 10.806c.343 2.002-1.758 3.528-3.555 2.583L24 37.613l-9.705 5.102c-1.797.945-3.898-.581-3.555-2.583l1.854-10.806l-7.851-7.653c-1.455-1.417-.652-3.887 1.357-4.179l10.85-1.577l4.853-9.832z" fill="currentColor"></path></g></svg>',

  'CommentNote20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.5 1A1.5 1.5 0 0 0 10 2.5v5A1.5 1.5 0 0 0 11.5 9h6A1.5 1.5 0 0 0 19 7.5v-5A1.5 1.5 0 0 0 17.5 1h-6zm1 5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1 0-1zM12 3.5a.5.5 0 0 1 .5-.5h4a.5.5 0 0 1 0 1h-4a.5.5 0 0 1-.5-.5zM4.6 3H9v4.5a2.5 2.5 0 0 0 2.5 2.5h6c.171 0 .338-.017.5-.05v2.326c0 1.418-1.164 2.566-2.6 2.566h-4.59l-4.011 2.961a1.009 1.009 0 0 1-1.4-.199a.978.978 0 0 1-.199-.59v-2.172h-.6c-1.436 0-2.6-1.149-2.6-2.566v-6.71C2 4.149 3.164 3 4.6 3z" fill="currentColor"></path></g></svg>',

  'ArrowRight20Filled': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 20 20"><g fill="none"><path d="M11.265 3.205a.75.75 0 0 0-1.03 1.09l5.239 4.955H2.75a.75.75 0 0 0 0 1.5h12.726l-5.241 4.957a.75.75 0 1 0 1.03 1.09l6.418-6.07a.995.995 0 0 0 .3-.566a.753.753 0 0 0-.002-.329a.995.995 0 0 0-.298-.557l-6.418-6.07z" fill="currentColor"></path></g></svg>',

  // Ant Design Icons (保持原有命名风格)
  'LinkOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M574 665.4a8.03 8.03 0 0 0-11.3 0L446.5 781.6c-53.8 53.8-144.6 59.5-204 0c-59.5-59.5-53.8-150.2 0-204l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3l-39.8-39.8a8.03 8.03 0 0 0-11.3 0L191.4 526.5c-84.6 84.6-84.6 221.5 0 306s221.5 84.6 306 0l116.2-116.2c3.1-3.1 3.1-8.2 0-11.3L574 665.4zm258.6-474c-84.6-84.6-221.5-84.6-306 0L410.3 307.6a8.03 8.03 0 0 0 0 11.3l39.7 39.7c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c53.8-53.8 144.6-59.5 204 0c59.5 59.5 53.8 150.2 0 204L665.3 562.6a8.03 8.03 0 0 0 0 11.3l39.8 39.8c3.1 3.1 8.2 3.1 11.3 0l116.2-116.2c84.5-84.6 84.5-221.5 0-306.1zM610.1 372.3a8.03 8.03 0 0 0-11.3 0L372.3 598.7a8.03 8.03 0 0 0 0 11.3l39.6 39.6c3.1 3.1 8.2 3.1 11.3 0l226.4-226.4c3.1-3.1 3.1-8.2 0-11.3l-39.5-39.6z" fill="currentColor"></path></svg>',

  'RollbackOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M793 242H366v-74c0-6.7-7.7-10.4-12.9-6.3l-142 112a8 8 0 0 0 0 12.6l142 112c5.2 4.1 12.9.4 12.9-6.3v-74h415v470H175c-4.4 0-8 3.6-8 8v60c0 4.4 3.6 8 8 8h618c35.3 0 64-28.7 64-64V306c0-35.3-28.7-64-64-64z" fill="currentColor"></path></svg>',

  'LikeOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 533.7c16.8-22.2 26.1-49.4 26.1-77.7c0-44.9-25.1-87.4-65.5-111.1a67.67 67.67 0 0 0-34.3-9.3H572.4l6-122.9c1.4-29.7-9.1-57.9-29.5-79.4A106.62 106.62 0 0 0 471 99.9c-52 0-98 35-111.8 85.1l-85.9 311H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h601.3c9.2 0 18.2-1.8 26.5-5.4c47.6-20.3 78.3-66.8 78.3-118.4c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c0-12.6-1.8-25-5.4-37c16.8-22.2 26.1-49.4 26.1-77.7c-.2-12.6-2-25.1-5.6-37.1zM184 852V568h81v284h-81zm636.4-353l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 16.5-7.2 32.2-19.6 43l-21.9 19l13.9 25.4a56.2 56.2 0 0 1 6.9 27.3c0 22.4-13.2 42.6-33.6 51.8H329V564.8l99.5-360.5a44.1 44.1 0 0 1 42.2-32.3c7.6 0 15.1 2.2 21.1 6.7c9.9 7.4 15.2 18.6 14.6 30.5l-9.6 198.4h314.4C829 418.5 840 436.9 840 456c0 16.5-7.2 32.1-19.6 43z" fill="currentColor"></path></svg>',

  'DislikeOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M885.9 490.3c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-28.3-9.3-55.5-26.1-77.7c3.6-12 5.4-24.4 5.4-37c0-51.6-30.7-98.1-78.3-118.4a66.1 66.1 0 0 0-26.5-5.4H144c-17.7 0-32 14.3-32 32v364c0 17.7 14.3 32 32 32h129.3l85.8 310.8C372.9 889 418.9 924 470.9 924c29.7 0 57.4-11.8 77.9-33.4c20.5-21.5 31-49.7 29.5-79.4l-6-122.9h239.9c12.1 0 23.9-3.2 34.3-9.3c40.4-23.5 65.5-66.1 65.5-111c0-28.3-9.3-55.5-26.1-77.7zM184 456V172h81v284h-81zm627.2 160.4H496.8l9.6 198.4c.6 11.9-4.7 23.1-14.6 30.5c-6.1 4.5-13.6 6.8-21.1 6.7a44.28 44.28 0 0 1-42.2-32.3L329 459.2V172h415.4a56.85 56.85 0 0 1 33.6 51.8c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-13.9 25.4l21.9 19a56.76 56.76 0 0 1 19.6 43c0 9.7-2.3 18.9-6.9 27.3l-14 25.5l21.9 19a56.76 56.76 0 0 1 19.6 43c0 19.1-11 37.5-28.8 48.4z" fill="currentColor"></path></svg>',

  'CommentOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M573 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40zm-280 0c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path><path d="M894 345c-48.1-66-115.3-110.1-189-130v.1c-17.1-19-36.4-36.5-58-52.1c-163.7-119-393.5-82.7-513 81c-96.3 133-92.2 311.9 6 439l.8 132.6c0 3.2.5 6.4 1.5 9.4c5.3 16.9 23.3 26.2 40.1 20.9L309 806c33.5 11.9 68.1 18.7 102.5 20.6l-.5.4c89.1 64.9 205.9 84.4 313 49l127.1 41.4c3.2 1 6.5 1.6 9.9 1.6c17.7 0 32-14.3 32-32V753c88.1-119.6 90.4-284.9 1-408zM323 735l-12-5l-99 31l-1-104l-8-9c-84.6-103.2-90.2-251.9-11-361c96.4-132.2 281.2-161.4 413-66c132.2 96.1 161.5 280.6 66 412c-80.1 109.9-223.5 150.5-348 102zm505-17l-8 10l1 104l-98-33l-12 5c-56 20.8-115.7 22.5-171 7l-.2-.1C613.7 788.2 680.7 742.2 729 676c76.4-105.3 88.8-237.6 44.4-350.4l.6.4c23 16.5 44.1 37.1 62 62c72.6 99.6 68.5 235.2-8 330z" fill="currentColor"></path><path d="M433 421c-23.1 0-41 17.9-41 40s17.9 40 41 40c21.1 0 39-17.9 39-40s-17.9-40-39-40z" fill="currentColor"></path></svg>',

  // Ionicons (保持原有命名风格)
  'IosCode': '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M332,142.7c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-13.8,13.2c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l95.8,91.5l-95.8,91.5c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l13.8,13.2c1.2,1.1,2.6,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l114.2-109c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L332,142.7z"></path><path d="M204,160.2c0-1.6-0.7-3.2-1.9-4.3l-13.8-13.2c-1.2-1.1-2.7-1.7-4.1-1.7s-3,0.6-4.1,1.7l-114.2,109c-1.2,1.1-1.9,2.7-1.9,4.3c0,1.6,0.7,3.2,1.9,4.3l114.2,109c1.2,1.1,2.7,1.7,4.1,1.7c1.5,0,3-0.6,4.1-1.7l13.8-13.2c1.2-1.1,1.9-2.7,1.9-4.3c0-1.6-0.7-3.2-1.9-4.3L106.3,256l95.8-91.5C203.3,163.4,204,161.8,204,160.2z"></path></g></svg>',

  'IosNotificationsOutline': '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M289.7,403c-6.1,0-11.4,4.2-12.7,10.2c-1,4.5-2.7,8.2-5,10.9c-1.3,1.5-5.1,5.9-16.1,5.9c-11,0-14.8-4.5-16.1-5.9' +
    'c-2.3-2.7-4-6.4-5-10.9c-1.3-6-6.6-10.2-12.7-10.2h0c-8.4,0-14.5,7.8-12.7,15.9c5,22.3,21,37.1,46.5,37.1s41.5-14.7,46.5-37.1' +
    'C304.2,410.8,298,403,289.7,403L289.7,403z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9' +
    'c0-13.3-10.8-24.6-24-24.6c-0.1,0-0.2,0-0.3,0c-0.1,0-0.2,0-0.3,0c-13.2,0-24,11.3-24,24.6v2.9c0,3.7-2.6,5-6.2,5.9' +
    'c-38.5,9.1-79.6,46.5-79.6,139.8c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8h140.4h139.7' +
    'C412.5,384,421.8,365.2,412,352.2z M373,358H139.8c-3.8,0-5.8-4.4-3.3-7.3c7-8,14.7-18.5,21-33.4c9.6-22.6,14.3-51.5,14.3-88.2' +
    'c0-37.3,7-66.5,20.9-86.8c12.4-18.2,27.9-25.1,38.7-27.6c8.4-2,14.4-5.8,18.6-10.5c3.2-3.6,8.7-3.8,11.9-0.2' +
    'c5.1,5.7,12,9.1,18.8,10.7c10.8,2.5,26.3,9.4,38.7,27.6c13.9,20.3,20.9,49.5,20.9,86.8c0,36.7,4.7,65.6,14.3,88.2' +
    'c6.5,15.2,14.4,25.9,21.5,33.9C378.3,353.9,376.5,358,373,358z"></path></g></svg>',

  'IosNotificationsOff': '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><g><path d="M255.9,456c31.1,0,48.1-22,48.1-53h-96.3C207.7,434,224.7,456,255.9,456z"></path><path d="M154.5,55c-2.5-4.3-7-6.8-11.6-7c0.1,0,0.2,0,0.3,0c-0.3,0-0.6,0-0.9,0c-0.1,0-0.2,0-0.3,0c-2.3,0-4.7,0.7-6.9,1.9' +
    'c-6.8,3.9-9.1,12.6-5.1,19.3L357.5,457c2.6,4.5,7.4,7,12.3,7c2.4,0,4.9-0.6,7.2-1.9c6.8-3.9,9.1-12.6,5.1-19.3L154.5,55z"></path><path d="M296.1,384L159,150.5c-8.2,20.2-13.3,46-13.3,78.6c0,90.9-30.3,102.7-45.7,123.1c-9.9,13.1-0.5,31.8,15.9,31.8l140.4,0' +
    'H296.1z"></path><path d="M412,352.2c-15.4-20.3-45.7-32.2-45.7-123.1c0-93.3-41.2-130.8-79.6-139.8c-3.6-0.9-6.2-2.1-6.2-5.9v-2.9' +
    'c0-13.4-11-24.7-24.4-24.6c-13.4-0.2-24.4,11.2-24.4,24.6v2.9c0,3.7-2.6,5-6.2,5.9c-8.7,2-17.5,5.5-25.9,10.8L366.1,384h29.9' +
    'C412.5,384,421.9,365.2,412,352.2z"></path></g></svg>',

  'IosClose': '<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 512 512" enable-background="new 0 0 512 512" xml:space="preserve"><path d="M278.6,256l68.2-68.2c6.2-6.2,6.2-16.4,0-22.6c-6.2-6.2-16.4-6.2-22.6,0L256,233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6,0' +
    'c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3l68.2,68.2l-68.2,68.2c-3.1,3.1-4.7,7.2-4.7,11.3c0,4.1,1.6,8.2,4.7,11.3' +
    'c6.2,6.2,16.4,6.2,22.6,0l68.2-68.2l68.2,68.2c6.2,6.2,16.4,6.2,22.6,0c6.2-6.2,6.2-16.4,0-22.6L278.6,256z"></path></svg>',

  // Lock and Unlock Icons (Ant Design style naming)
  'LockOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464h-68V240c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zM332 240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v224H332V240zm460 600H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',
  'UnlockOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><path d="M832 464H332V240c0-30.9 25.1-56 56-56h248c30.9 0 56 25.1 56 56v68c0 4.4 3.6 8 8 8h56c4.4 0 8-3.6 8-8v-68c0-70.7-57.3-128-128-128H388c-70.7 0-128 57.3-128 128v224h-68c-17.7 0-32 14.3-32 32v384c0 17.7 14.3 32 32 32h640c17.7 0 32-14.3 32-32V496c0-17.7-14.3-32-32-32zm-40 376H232V536h560v304zM484 701v53c0 4.4 3.6 8 8 8h40c4.4 0 8-3.6 8-8v-53a48.01 48.01 0 1 0-56 0z" fill="currentColor"></path></svg>',

  // Color and Text Color Icons (Fluent style naming)
  'TextColor24Regular': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M17.75 14.5A2.25 2.25 0 0 1 20 16.75v3A2.25 2.25 0 0 1 17.75 22H5.25A2.25 2.25 0 0 1 3 19.75v-3a2.25 2.25 0 0 1 2.25-2.25h12.5zm0 1.5H5.25a.75.75 0 0 0-.75.75v3c0 .415.336.75.75.75h12.5a.75.75 0 0 0 .75-.75v-3a.75.75 0 0 0-.75-.75zM7.053 11.97l3.753-9.496c.236-.595 1.043-.63 1.345-.104l.05.105l3.747 9.5a.75.75 0 0 1-1.352.643l-.044-.092L13.556 10H9.443l-.996 2.52a.75.75 0 0 1-.876.454l-.097-.031a.75.75 0 0 1-.453-.876l.032-.098l3.753-9.495l-3.753 9.495zm4.45-7.178L10.036 8.5h2.928l-1.461-3.708z" fill="currentColor"></path></g></svg>',
  'Color24Regular': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none"><path d="M3.839 5.858c2.94-3.916 9.03-5.055 13.364-2.36c4.28 2.66 5.854 7.777 4.1 12.577c-1.655 4.533-6.016 6.328-9.159 4.048c-1.177-.854-1.634-1.925-1.854-3.664l-.106-.987l-.045-.398c-.123-.934-.311-1.352-.705-1.572c-.535-.298-.892-.305-1.595-.033l-.351.146l-.179.078c-1.014.44-1.688.595-2.541.416l-.2-.047l-.164-.047c-2.789-.864-3.202-4.647-.565-8.157zm.984 6.716l.123.037l.134.03c.439.087.814.015 1.437-.242l.602-.257c1.202-.493 1.985-.54 3.046.05c.917.512 1.275 1.298 1.457 2.66l.053.459l.055.532l.047.422c.172 1.361.485 2.09 1.248 2.644c2.275 1.65 5.534.309 6.87-3.349c1.516-4.152.174-8.514-3.484-10.789c-3.675-2.284-8.899-1.306-11.373 1.987c-2.075 2.763-1.82 5.28-.215 5.816zm11.225-1.994a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm.494 3.488a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zM14.07 7.577a1.25 1.25 0 1 1 2.415-.647a1.25 1.25 0 0 1-2.415.647zm-.028 8.998a1.25 1.25 0 1 1 2.414-.647a1.25 1.25 0 0 1-2.414.647zm-3.497-9.97a1.25 1.25 0 1 1 2.415-.646a1.25 1.25 0 0 1-2.415.646z" fill="currentColor"></path></g></svg>',

  // Format Painter Icon (Ant Design style naming)
  'FormatPainterOutlined': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1024 1024"><defs></defs><path d="M840 192h-56v-72c0-13.3-10.7-24-24-24H168c-13.3 0-24 10.7-24 24v272c0 13.3 10.7 24 24 24h592c13.3 0 24-10.7 24-24V256h32v200H465c-22.1 0-40 17.9-40 40v136h-44c-4.4 0-8 3.6-8 8v228c0 .6.1 1.3.2 1.9c-.1 2-.2 4.1-.2 6.1c0 46.4 37.6 84 84 84s84-37.6 84-84c0-2.1-.1-4.1-.2-6.1c.1-.6.2-1.2.2-1.9V640c0-4.4-3.6-8-8-8h-44V520h351c22.1 0 40-17.9 40-40V232c0-22.1-17.9-40-40-40zM720 352H208V160h512v192zM477 876c0 11-9 20-20 20s-20-9-20-20V696h40v180z" fill="currentColor"></path></svg>',

  // Tabler Icons (保持原有命名风格)
  'Blockquote': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 15h15"></path><path d="M21 19H6"></path><path d="M15 11h6"></path><path d="M21 7h-6"></path><path d="M9 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path><path d="M3 9h1a1 1 0 1 1-1 1V7.5a2 2 0 0 1 2-2"></path></g></svg>',

  // Carbon Icons (保持原有命名风格)
  'UserMultiple': '<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 32 32"><path d="M30 30h-2v-5a5.006 5.006 0 0 0-5-5v-2a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M22 30h-2v-5a5.006 5.006 0 0 0-5-5H9a5.006 5.006 0 0 0-5 5v5H2v-5a7.008 7.008 0 0 1 7-7h6a7.008 7.008 0 0 1 7 7z" fill="currentColor"></path><path d="M20 2v2a5 5 0 0 1 0 10v2a7 7 0 0 0 0-14z" fill="currentColor"></path><path d="M12 4a5 5 0 1 1-5 5a5 5 0 0 1 5-5m0-2a7 7 0 1 0 7 7a7 7 0 0 0-7-7z" fill="currentColor"></path></svg>',
} as const;

// 图标名称类型
export type IconName = keyof typeof iconSvgs;

// 获取图标SVG字符串
export function getIconSvg(name: IconName): string {
  return iconSvgs[name];
}

// 检查图标是否存在
export function hasIcon(name: string): name is IconName {
  return name in iconSvgs;
}

// 获取所有图标名称
export function getAllIconNames(): IconName[] {
  return Object.keys(iconSvgs) as IconName[];
}

// 创建图标元素（用于TS/JS中直接使用）
export function createIconElement(name: IconName, options?: {
  size?: string | number;
  color?: string;
  className?: string;
}): HTMLElement {
  const { size = '1em', color = 'currentColor', className = '' } = options || {};

  if (!hasIcon(name)) {
    console.warn(`Icon "${name}" not found`);
    const span = document.createElement('span');
    span.textContent = name;
    span.className = 'icon-fallback';
    return span;
  }

  const wrapper = document.createElement('span');
  wrapper.className = `icon-wrapper ${className}`.trim();

  let svg = getIconSvg(name);

  // 替换颜色
  if (color !== 'currentColor') {
    svg = svg.replace(/fill="currentColor"/g, `fill="${color}"`);
    svg = svg.replace(/stroke="currentColor"/g, `stroke="${color}"`);
  }

  // 设置尺寸
  const sizeValue = typeof size === 'number' ? `${size}px` : size;
  svg = svg.replace(/width="[^"]*"/g, `width="${sizeValue}"`);
  svg = svg.replace(/height="[^"]*"/g, `height="${sizeValue}"`);

  wrapper.innerHTML = svg;
  wrapper.style.display = 'inline-flex';
  wrapper.style.alignItems = 'center';
  wrapper.style.justifyContent = 'center';
  wrapper.style.width = sizeValue;
  wrapper.style.height = sizeValue;
  wrapper.style.color = color;

  return wrapper;
}

// 获取图标HTML字符串（用于innerHTML）
export function getIconHtml(name: IconName, options?: {
  size?: string | number;
  color?: string;
  className?: string;
}): string {
  const element = createIconElement(name, options);
  return element.outerHTML;
}

// 注意：IconName 类型已在上面定义，不需要重复导出

// 导出版本信息
export const VERSION = '1.0.0';

// 导出图标统计信息
export function getIconCount(): number {
  return Object.keys(iconSvgs).length;
}
