/**
 * 图标系统主入口文件
 * 统一导出所有图标相关的功能
 */

// 核心功能
export * from './index';
export type * from './types';
export {
  searchIcons,
  getAllCategories,
  getIconMetadata,
  getIconsByCategory,
  getIconStats,
  generateTags,
  generateDescription
} from './utils';

// 配置和缓存
export * from './config';
export * from './cache';

// Vue组件
export { default as Icon } from './Icon.vue';
export { default as IconDemo } from './IconDemo.vue';
export { default as IconBrowser } from './IconBrowser.vue';

// 安装插件
export { default as IconPlugin, install } from './install';

// 初始化函数
import { initCache } from './cache';
import { loadConfigFromEnv } from './config';

export function initIconSystem() {
  // 从环境变量加载配置
  loadConfigFromEnv();
  
  // 初始化缓存
  initCache();
  
  console.log('图标系统已初始化');
}

// 自动初始化（可选）
if (typeof window !== 'undefined') {
  // 浏览器环境下自动初始化
  initIconSystem();
}
