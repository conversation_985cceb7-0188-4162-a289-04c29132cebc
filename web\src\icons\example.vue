<template>
  <div class="icon-example">
    <h1>图标系统使用示例</h1>
    
    <!-- 基础使用 -->
    <section>
      <h2>1. 基础使用</h2>
      <div class="example-row">
        <Icon name="search" />
        <Icon name="add" size="20" />
        <Icon name="star" color="#f5222d" />
        <code>&lt;Icon name="search" /&gt;</code>
      </div>
    </section>
    
    <!-- 替换原有vicons -->
    <section>
      <h2>2. 替换vicons使用</h2>
      <div class="example-row">
        <!-- 原来的写法 -->
        <div class="old-way">
          <span class="label">原来:</span>
          <code>&lt;NIcon&gt;&lt;TextBold20Filled /&gt;&lt;/NIcon&gt;</code>
        </div>
        
        <!-- 新的写法 -->
        <div class="new-way">
          <span class="label">现在:</span>
          <Icon name="text-bold" />
          <code>&lt;Icon name="text-bold" /&gt;</code>
        </div>
        
        <!-- 兼容写法 -->
        <div class="compat-way">
          <span class="label">兼容:</span>
          <VIcon library="fluent" iconName="TextBold20Filled" />
          <code>&lt;VIcon library="fluent" iconName="TextBold20Filled" /&gt;</code>
        </div>
      </div>
    </section>
    
    <!-- 工具栏示例 -->
    <section>
      <h2>3. 工具栏示例</h2>
      <div class="toolbar">
        <button class="toolbar-btn" @click="handleAction('bold')">
          <Icon name="text-bold" size="16" />
          <span>加粗</span>
        </button>
        <button class="toolbar-btn" @click="handleAction('italic')">
          <Icon name="text-italic" size="16" />
          <span>斜体</span>
        </button>
        <button class="toolbar-btn" @click="handleAction('underline')">
          <Icon name="text-underline" size="16" />
          <span>下划线</span>
        </button>
        <div class="toolbar-divider"></div>
        <button class="toolbar-btn" @click="handleAction('undo')">
          <Icon name="arrow-undo" size="16" />
          <span>撤销</span>
        </button>
        <button class="toolbar-btn" @click="handleAction('redo')">
          <Icon name="arrow-redo" size="16" />
          <span>重做</span>
        </button>
      </div>
    </section>
    
    <!-- 动态图标 -->
    <section>
      <h2>4. 动态图标</h2>
      <div class="dynamic-icons">
        <button @click="toggleLike" class="like-btn">
          <Icon 
            :name="liked ? 'star' : 'star'" 
            :color="liked ? '#f5222d' : '#999'" 
            size="20" 
          />
          <span>{{ liked ? '已收藏' : '收藏' }}</span>
        </button>
        
        <button @click="addDynamicIcon" class="add-btn">
          <Icon name="add" size="16" />
          <span>添加图标</span>
        </button>
        
        <div ref="dynamicContainer" class="dynamic-container"></div>
      </div>
    </section>
    
    <!-- 配置示例 -->
    <section>
      <h2>5. 配置示例</h2>
      <div class="config-panel">
        <div class="config-item">
          <label>默认尺寸:</label>
          <select v-model="defaultSize" @change="updateIconConfig">
            <option value="16">16px</option>
            <option value="20">20px</option>
            <option value="24">24px</option>
          </select>
        </div>
        
        <div class="config-item">
          <label>默认颜色:</label>
          <input 
            v-model="defaultColor" 
            type="color" 
            @change="updateIconConfig"
          />
        </div>
        
        <div class="config-item">
          <label>启用缓存:</label>
          <input 
            v-model="enableCache" 
            type="checkbox" 
            @change="updateIconConfig"
          />
        </div>
        
        <div class="config-preview">
          <span>预览:</span>
          <Icon name="search" :size="defaultSize" :color="defaultColor" />
        </div>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Icon from './Icon.vue';
// import VIcon from './VIcon.vue'; // VIcon 已移除
import { createIconElement } from './index';
import { updateConfig } from './config';

const liked = ref(false);
const dynamicContainer = ref<HTMLElement>();

// 配置相关
const defaultSize = ref('20');
const defaultColor = ref('#1890ff');
const enableCache = ref(true);

// 处理工具栏操作
const handleAction = (action: string) => {
  console.log(`执行操作: ${action}`);
};

// 切换收藏状态
const toggleLike = () => {
  liked.value = !liked.value;
};

// 添加动态图标
const addDynamicIcon = () => {
  if (!dynamicContainer.value) return;
  
  const icons = ['search', 'add', 'star', 'comment-note', 'arrow-right'];
  const randomIcon = icons[Math.floor(Math.random() * icons.length)];
  
  const iconElement = createIconElement(randomIcon as any, {
    size: '20px',
    color: '#1890ff',
    className: 'dynamic-icon'
  });
  
  dynamicContainer.value.appendChild(iconElement);
};

// 更新图标配置
const updateIconConfig = () => {
  updateConfig({
    defaultSize: `${defaultSize.value}px`,
    defaultColor: defaultColor.value,
    enableCache: enableCache.value,
  });
};

onMounted(() => {
  console.log('图标系统示例已加载');
});
</script>

<style scoped>
.icon-example {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

section {
  margin-bottom: 40px;
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

h2 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #1890ff;
  padding-bottom: 8px;
}

.example-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.old-way, .new-way, .compat-way {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  background: #fafafa;
}

.label {
  font-weight: 500;
  color: #666;
  min-width: 40px;
}

code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
}

.toolbar {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 6px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: 1px solid transparent;
  border-radius: 4px;
  background: transparent;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.toolbar-btn:hover {
  background: white;
  border-color: #d9d9d9;
}

.toolbar-divider {
  width: 1px;
  height: 20px;
  background: #d9d9d9;
  margin: 0 4px;
}

.dynamic-icons {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.like-btn, .add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  width: fit-content;
  transition: all 0.2s ease;
}

.like-btn:hover, .add-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.dynamic-container {
  min-height: 60px;
  padding: 16px;
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.config-panel {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.config-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.config-item label {
  font-weight: 500;
  color: #333;
}

.config-item select,
.config-item input[type="color"] {
  padding: 4px 8px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

.config-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f0f8ff;
  border-radius: 6px;
}

:deep(.dynamic-icon) {
  margin-right: 4px;
}
</style>
