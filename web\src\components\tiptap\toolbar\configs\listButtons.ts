import Icon from '@/icons/Icon.vue'
import { h } from 'vue'

import type { ToolbarButtonConfig } from './types'

/**
 * 列表按钮配置
 */
export const listButtons: ToolbarButtonConfig[] = [
  {
    icon: () => h(Icon, { name: 'TextBulletList16Filled' }),
    extensionName: 'bulletList',
    trigger: (editor) => editor?.chain().focus().toggleBulletList().run(),
    isActive: (editor) => editor?.isActive('bulletList'),
    tooltip: '无序列表',
  },
  {
    icon: () => h(Icon, { name: 'TextNumberList16Filled' }),
    extensionName: 'orderedList',
    trigger: (editor) => editor?.chain().focus().toggleOrderedList().run(),
    isActive: (editor) => editor?.isActive('orderedList'),
    tooltip: '有序列表',
  },
  {
    icon: () => h(Icon, { name: 'TaskList24Filled' }),
    extensionName: 'taskList',
    trigger: (editor) => editor?.chain().focus().toggleTaskList().run(),
    isActive: (editor) => editor?.isActive('taskList'),
    tooltip: '任务列表',
  },
]
